#!/usr/bin/env python3
"""
FireRedASR 快速下载脚本
"""

def quick_download():
    """快速下载 FireRedASR 模型"""
    import subprocess
    import sys
    import os
    
    print("正在安装依赖...")
    packages = ["modelscope", "transformers", "torch", "torchaudio"]
    for pkg in packages:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pkg, "-q"])
    
    print("依赖安装完成，开始下载 FireRedASR 模型...")
    
    from modelscope import snapshot_download
    
    # 下载主要的 FireRedASR 模型
    models_to_download = [
        ("FireRedASR-LLM-L", "pengzhendong/FireRedASR-LLM-L"),
        ("FireRedASR-AED-L", "pengzhendong/FireRedASR-AED-L")
    ]
    
    cache_dir = "/mnt/2T-mac/models"
    os.makedirs(cache_dir, exist_ok=True)
    
    for name, model_id in models_to_download:
        print(f"\n正在下载 {name}...")
        try:
            model_dir = snapshot_download(model_id, cache_dir=cache_dir)
            print(f"✅ {name} 下载完成: {model_dir}")
        except Exception as e:
            print(f"❌ {name} 下载失败: {e}")
    
    print("\n🎉 FireRedASR 模型下载完成!")

if __name__ == "__main__":
    quick_download()
