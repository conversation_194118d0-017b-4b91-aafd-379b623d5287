#!/usr/bin/env python3
"""
FireRedASR 模型测试脚本
"""

import os
import sys
import torch

def test_fireredasr_models():
    """测试 FireRedASR 模型"""
    print("FireRedASR 模型测试")
    print("=" * 50)
    
    # 模型路径
    models = {
        "FireRedASR-LLM-L": "/mnt/2T-mac/models/pengzhendong/FireRedASR-LLM-L",
        "FireRedASR-AED-L": "/mnt/2T-mac/models/pengzhendong/FireRedASR-AED-L"
    }
    
    for name, path in models.items():
        print(f"\n🔍 检查 {name}...")
        
        if not os.path.exists(path):
            print(f"❌ 模型路径不存在: {path}")
            continue
        
        print(f"✅ 模型路径存在: {path}")
        
        # 检查模型文件
        files = os.listdir(path)
        print(f"📁 文件数量: {len(files)}")
        
        # 检查关键文件
        key_files = ["model.pth.tar", "config.yaml", "README.md"]
        found_files = []
        
        for key_file in key_files:
            if key_file in files:
                found_files.append(key_file)
                print(f"✅ 找到关键文件: {key_file}")
            else:
                print(f"⚠️  缺少文件: {key_file}")
        
        # 计算模型大小
        try:
            import subprocess
            result = subprocess.run(['du', '-sh', path], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                size = result.stdout.split()[0]
                print(f"💾 模型大小: {size}")
        except:
            print(f"💾 模型大小: 计算中...")
        
        print(f"✅ {name} 验证通过")

def test_model_loading():
    """测试模型加载"""
    print("\n" + "=" * 50)
    print("模型加载测试")
    print("=" * 50)
    
    # 尝试加载 FireRedASR 模型
    model_path = "/mnt/2T-mac/models/pengzhendong/FireRedASR-LLM-L"
    
    try:
        print("🔄 尝试加载 FireRedASR-LLM-L...")
        
        # 检查 PyTorch 版本
        print(f"PyTorch 版本: {torch.__version__}")
        print(f"CUDA 可用: {torch.cuda.is_available()}")
        
        # 尝试加载模型文件
        model_file = os.path.join(model_path, "model.pth.tar")
        if os.path.exists(model_file):
            print(f"✅ 找到模型文件: {model_file}")
            
            # 获取文件大小
            file_size = os.path.getsize(model_file)
            print(f"📊 模型文件大小: {file_size / (1024**3):.2f} GB")
            
            # 尝试加载模型权重（仅检查格式）
            try:
                checkpoint = torch.load(model_file, map_location='cpu')
                print(f"✅ 模型文件格式正确")
                
                if isinstance(checkpoint, dict):
                    print(f"📋 检查点包含的键: {list(checkpoint.keys())[:5]}...")
                
            except Exception as e:
                print(f"⚠️  模型加载警告: {e}")
        
        print("✅ FireRedASR 模型基础检查通过")
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")

def show_usage_info():
    """显示使用信息"""
    print("\n" + "=" * 50)
    print("FireRedASR 使用信息")
    print("=" * 50)
    
    print("""
📖 FireRedASR 模型说明:
- FireRedASR-LLM-L: 大语言模型版本，支持中英文语音识别
- FireRedASR-AED-L: AED架构版本，支持中文方言和英文

🚀 使用方法:
1. 使用 ModelScope 加载:
   from modelscope import AutoModel, AutoTokenizer
   model = AutoModel.from_pretrained('/mnt/2T-mac/models/pengzhendong/FireRedASR-LLM-L')

2. 使用 PyTorch 直接加载:
   import torch
   checkpoint = torch.load('/mnt/2T-mac/models/pengzhendong/FireRedASR-LLM-L/model.pth.tar')

📁 模型位置:
- FireRedASR-LLM-L: /mnt/2T-mac/models/pengzhendong/FireRedASR-LLM-L
- FireRedASR-AED-L: /mnt/2T-mac/models/pengzhendong/FireRedASR-AED-L

💡 注意事项:
- 模型需要配合相应的音频处理库使用
- 建议使用 GPU 进行推理以获得更好的性能
- 支持中文、英文和中文方言的语音识别
    """)

def main():
    """主函数"""
    # 测试模型文件
    test_fireredasr_models()
    
    # 测试模型加载
    test_model_loading()
    
    # 显示使用信息
    show_usage_info()
    
    print("\n🎉 FireRedASR 模型测试完成!")

if __name__ == "__main__":
    main()
