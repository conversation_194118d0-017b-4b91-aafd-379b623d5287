#!/usr/bin/env python3
"""
MiDashengLM-7B 快速部署脚本
"""

def quick_setup():
    """快速设置和测试"""
    print("正在安装必要依赖...")

    import subprocess
    import sys

    # 安装核心依赖
    packages = ["modelscope", "transformers", "torch", "accelerate"]
    for pkg in packages:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pkg, "-q"])

    print("依赖安装完成，开始下载模型...")

    # 下载模型
    from modelscope import snapshot_download
    model_dir = snapshot_download("midasheng/midashenglm-7b", cache_dir="/mnt/2T-mac/models")

    print(f"模型下载完成: {model_dir}")

    # 快速测试
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch

    print("加载模型中...")
    tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        model_dir,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map="auto" if torch.cuda.is_available() else None,
        trust_remote_code=True
    )

    print("模型加载完成！开始测试...")

    # 简单测试
    prompt = "你好，请介绍一下你自己。"
    inputs = tokenizer(prompt, return_tensors="pt")

    if torch.cuda.is_available():
        inputs = inputs.to("cuda")

    with torch.no_grad():
        outputs = model.generate(**inputs, max_new_tokens=100, temperature=0.7)

    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    print(f"\n测试结果:")
    print(f"输入: {prompt}")
    print(f"输出: {response[len(prompt):].strip()}")

    print("\n✅ 模型部署成功！")
    return model, tokenizer

if __name__ == "__main__":
    quick_setup()
