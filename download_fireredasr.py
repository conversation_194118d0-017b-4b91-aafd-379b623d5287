#!/usr/bin/env python3
"""
FireRedASR 模型下载脚本
支持下载多个 FireRedASR 模型变体到 2T 磁盘
"""

import os
import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """安装必要的依赖"""
    print("=== 安装依赖 ===")
    
    packages = [
        "modelscope",
        "transformers",
        "torch",
        "torchaudio",
        "librosa",
        "soundfile"
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package, "-q"])
        except subprocess.CalledProcessError as e:
            print(f"安装 {package} 失败: {e}")
            return False
    
    print("依赖安装完成!\n")
    return True

def download_fireredasr_models():
    """下载 FireRedASR 模型"""
    print("=== 下载 FireRedASR 模型 ===")
    
    # 目标目录
    cache_dir = "/mnt/2T-mac/models"
    
    # 确保目录存在
    os.makedirs(cache_dir, exist_ok=True)
    
    # FireRedASR 模型列表
    models = [
        {
            "name": "FireRedASR-LLM-L",
            "model_id": "pengzhendong/FireRedASR-LLM-L",
            "description": "FireRedASR LLM 大模型版本，支持中英文语音识别"
        },
        {
            "name": "FireRedASR-AED-L", 
            "model_id": "pengzhendong/FireRedASR-AED-L",
            "description": "FireRedASR AED 大模型版本，支持中文方言和英文"
        },
        {
            "name": "FireRedASR-ONNX",
            "model_id": "csukuangfj/sherpa-onnx-fire-red-asr-large-zh_en-2025-02-16",
            "description": "FireRedASR ONNX 版本，优化推理速度"
        }
    ]
    
    downloaded_models = []
    
    try:
        from modelscope import snapshot_download
        
        for model in models:
            print(f"\n正在下载 {model['name']}...")
            print(f"描述: {model['description']}")
            print(f"模型ID: {model['model_id']}")
            print("这可能需要几分钟时间，请耐心等待...")
            
            try:
                model_dir = snapshot_download(
                    model_id=model['model_id'],
                    cache_dir=cache_dir,
                    revision="master"
                )
                
                print(f"✅ {model['name']} 下载完成!")
                print(f"保存路径: {model_dir}")
                
                downloaded_models.append({
                    "name": model['name'],
                    "path": model_dir,
                    "description": model['description']
                })
                
            except Exception as e:
                print(f"❌ {model['name']} 下载失败: {e}")
                continue
        
        return downloaded_models
        
    except Exception as e:
        print(f"下载过程出错: {e}")
        return []

def test_fireredasr_model(model_path, model_name):
    """测试 FireRedASR 模型"""
    print(f"\n=== 测试 {model_name} ===")
    
    try:
        # 检查模型文件
        if not os.path.exists(model_path):
            print(f"❌ 模型路径不存在: {model_path}")
            return False
        
        print(f"✅ 模型路径存在: {model_path}")
        
        # 列出模型文件
        files = os.listdir(model_path)
        print(f"模型文件数量: {len(files)}")
        
        # 检查关键文件
        key_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
        found_files = []
        
        for file in files:
            if any(key in file.lower() for key in ["config", "model", "tokenizer"]):
                found_files.append(file)
        
        if found_files:
            print(f"找到关键文件: {found_files[:5]}...")  # 只显示前5个
        
        print(f"✅ {model_name} 验证通过")
        return True
        
    except Exception as e:
        print(f"❌ {model_name} 测试失败: {e}")
        return False

def show_download_summary(downloaded_models):
    """显示下载总结"""
    print("\n" + "="*60)
    print("FireRedASR 模型下载总结")
    print("="*60)
    
    if not downloaded_models:
        print("❌ 没有成功下载任何模型")
        return
    
    print(f"✅ 成功下载 {len(downloaded_models)} 个模型:")
    
    total_size = 0
    for model in downloaded_models:
        print(f"\n📦 {model['name']}")
        print(f"   路径: {model['path']}")
        print(f"   描述: {model['description']}")
        
        # 计算模型大小
        try:
            import subprocess
            result = subprocess.run(['du', '-sh', model['path']], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                size = result.stdout.split()[0]
                print(f"   大小: {size}")
        except:
            print(f"   大小: 计算中...")
    
    print(f"\n💾 所有模型保存在: /mnt/2T-mac/models/")
    print("🎉 FireRedASR 模型下载完成!")

def create_usage_example():
    """创建使用示例文件"""
    example_code = '''#!/usr/bin/env python3
"""
FireRedASR 模型使用示例
"""

import torch
from modelscope import AutoModel, AutoTokenizer

def load_fireredasr_model(model_path):
    """加载 FireRedASR 模型"""
    try:
        # 加载模型和分词器
        model = AutoModel.from_pretrained(model_path, trust_remote_code=True)
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        
        print(f"✅ 模型加载成功: {model_path}")
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None

def main():
    """主函数"""
    # FireRedASR 模型路径
    models = {
        "LLM-L": "/mnt/2T-mac/models/pengzhendong/FireRedASR-LLM-L",
        "AED-L": "/mnt/2T-mac/models/pengzhendong/FireRedASR-AED-L",
        "ONNX": "/mnt/2T-mac/models/csukuangfj/sherpa-onnx-fire-red-asr-large-zh_en-2025-02-16"
    }
    
    for name, path in models.items():
        print(f"\\n测试 FireRedASR-{name}...")
        model, tokenizer = load_fireredasr_model(path)
        
        if model is not None:
            print(f"FireRedASR-{name} 可以正常使用")
        else:
            print(f"FireRedASR-{name} 加载失败")

if __name__ == "__main__":
    main()
'''
    
    with open("fireredasr_usage_example.py", "w", encoding="utf-8") as f:
        f.write(example_code)
    
    print("✅ 使用示例文件已创建: fireredasr_usage_example.py")

def main():
    """主函数"""
    print("FireRedASR 模型下载工具")
    print("="*50)
    
    # 检查目标目录
    target_dir = "/mnt/2T-mac/models"
    if not os.path.exists(target_dir):
        print(f"❌ 目标目录不存在: {target_dir}")
        print("请确保 2T 磁盘已正确挂载")
        return
    
    print(f"✅ 目标目录存在: {target_dir}")
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，退出程序")
        return
    
    # 下载模型
    downloaded_models = download_fireredasr_models()
    
    # 测试模型
    for model in downloaded_models:
        test_fireredasr_model(model['path'], model['name'])
    
    # 显示总结
    show_download_summary(downloaded_models)
    
    # 创建使用示例
    create_usage_example()

if __name__ == "__main__":
    main()
