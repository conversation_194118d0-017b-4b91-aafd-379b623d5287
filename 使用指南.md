# MiDashengLM-7B 使用指南

## 模型简介

MiDashengLM-7B 是小米开源的声音理解大模型，具有以下特点：

- **参数规模**: 7B参数
- **专业领域**: 音频理解和文本生成
- **性能表现**: 在22个公开评测中达到SOTA性能
- **推理速度**: 相比同类模型提升20倍
- **开源协议**: 完全开源，支持商用

## 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者手动安装核心依赖
pip install modelscope transformers torch accelerate
```

### 2. 模型下载

**方法一：一键下载**
```bash
python quick_deploy.py
```

**方法二：完整部署**
```bash
python deploy_midashenglm.py
```

**方法三：手动下载**
```python
from modelscope import snapshot_download
model_dir = snapshot_download("midasheng/midashenglm-7b", cache_dir="./models")
```

### 3. 模型测试

```bash
python test_model.py
```

## 使用示例

### 基础对话

```python
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 加载模型
model_dir = "./models/midasheng/midashenglm-7b"
tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(
    model_dir,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True
)

# 对话示例
prompt = "请解释一下深度学习的基本概念"
inputs = tokenizer(prompt, return_tensors="pt").to("cuda")

with torch.no_grad():
    outputs = model.generate(
        **inputs,
        max_new_tokens=200,
        temperature=0.7,
        do_sample=True
    )

response = tokenizer.decode(outputs[0], skip_special_tokens=True)
print(response[len(prompt):].strip())
```

### 创意写作

```python
prompt = "写一首关于人工智能的现代诗"
# ... 使用相同的推理代码 ...
```

### 问答系统

```python
prompt = "什么是Transformer架构？它有哪些优势？"
# ... 使用相同的推理代码 ...
```

## 参数调优

### 生成参数说明

- `max_new_tokens`: 最大生成长度（推荐100-500）
- `temperature`: 随机性控制（0.1-1.0，越小越确定）
- `top_p`: 核采样参数（0.8-0.95）
- `top_k`: 候选词数量（10-50）
- `do_sample`: 是否启用采样（True/False）

### 性能优化

**GPU优化**
```python
# 使用半精度
torch_dtype=torch.float16

# 自动设备分配
device_map="auto"

# 启用Flash Attention（如果支持）
attn_implementation="flash_attention_2"
```

**CPU优化**
```python
# 使用全精度
torch_dtype=torch.float32

# 减少生成长度
max_new_tokens=100

# 关闭采样提高速度
do_sample=False
```

## 应用场景

### 1. 智能客服
- 自然语言理解
- 多轮对话支持
- 情感识别

### 2. 内容创作
- 文章写作
- 创意文案
- 诗歌创作

### 3. 教育辅助
- 知识问答
- 学习指导
- 概念解释

### 4. 代码助手
- 代码解释
- 编程建议
- 技术文档

## 常见问题

### Q: 显存不足怎么办？
A: 
1. 使用CPU推理：移除`device_map="auto"`
2. 使用8bit量化：`load_in_8bit=True`
3. 使用4bit量化：`load_in_4bit=True`

### Q: 推理速度慢？
A: 
1. 使用GPU加速
2. 减少`max_new_tokens`
3. 设置`do_sample=False`
4. 使用批处理

### Q: 生成质量不佳？
A: 
1. 调整`temperature`参数
2. 优化prompt设计
3. 使用更好的采样策略

### Q: 模型下载失败？
A: 
1. 检查网络连接
2. 使用代理或镜像
3. 分段下载重试

## 技术支持

- **模型主页**: https://modelscope.cn/models/midasheng/midashenglm-7b
- **魔搭社区**: https://modelscope.cn/
- **GitHub**: 相关开源项目
- **论文**: 查看技术细节

## 许可证

请参考模型页面的许可证信息，确保合规使用。

## 更新日志

- **2025-08-06**: 初始部署版本
- 支持魔搭社区下载
- 提供完整使用示例
- 优化推理性能
