#!/usr/bin/env python3
"""
MiDashengLM-7B 模型测试脚本
"""

import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

def test_model():
    """测试已下载的模型"""
    model_dir = "/mnt/2T-mac/models/midasheng/midashenglm-7b"

    if not os.path.exists(model_dir):
        print("❌ 模型目录不存在，请先运行下载脚本")
        return False

    print("🔍 检查模型文件...")
    required_files = ["config.json", "tokenizer.json", "model.safetensors.index.json"]
    for file in required_files:
        if not os.path.exists(os.path.join(model_dir, file)):
            print(f"❌ 缺少文件: {file}")
            return False

    print("✅ 模型文件检查通过")

    try:
        print("📥 加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
        print("✅ 分词器加载成功")

        print("📥 加载模型...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🖥️  使用设备: {device}")

        model = AutoModelForCausalLM.from_pretrained(
            model_dir,
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None,
            trust_remote_code=True
        )

        if device == "cpu":
            model = model.to(device)

        print("✅ 模型加载成功")

        # 简单测试
        print("\n🧪 开始推理测试...")
        test_prompt = "你好，请简单介绍一下你自己。"
        print(f"输入: {test_prompt}")

        inputs = tokenizer(test_prompt, return_tensors="pt")
        if device == "cuda":
            inputs = inputs.to(device)

        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )

        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(test_prompt):].strip()

        print(f"输出: {generated_text}")
        print("\n✅ 模型测试成功！")

        return True

    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

if __name__ == "__main__":
    print("MiDashengLM-7B 模型测试")
    print("=" * 50)
    test_model()
