#!/usr/bin/env python3
"""
MiDashengLM-7B 模型下载和部署脚本
小米声音理解大模型 - 魔搭社区版本
"""

import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import warnings
warnings.filterwarnings("ignore")

def check_environment():
    """检查运行环境"""
    print("=== 环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    print()

def install_dependencies():
    """安装必要的依赖"""
    print("=== 安装依赖 ===")
    import subprocess
    
    packages = [
        "modelscope",
        "transformers>=4.35.0",
        "torch",
        "accelerate",
        "sentencepiece",
        "protobuf"
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError as e:
            print(f"安装 {package} 失败: {e}")
            return False
    
    print("依赖安装完成!\n")
    return True

def download_model():
    """从魔搭社区下载模型"""
    print("=== 下载模型 ===")
    try:
        from modelscope import snapshot_download
        
        model_id = "midasheng/midashenglm-7b"
        cache_dir = "./models"
        
        print(f"正在从魔搭社区下载模型: {model_id}")
        print("这可能需要几分钟时间，请耐心等待...")
        
        model_dir = snapshot_download(
            model_id=model_id,
            cache_dir=cache_dir,
            revision="master"
        )
        
        print(f"模型下载完成! 保存路径: {model_dir}")
        return model_dir
        
    except Exception as e:
        print(f"模型下载失败: {e}")
        return None

def load_model(model_dir):
    """加载模型和分词器"""
    print("=== 加载模型 ===")
    try:
        # 检查设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {device}")
        
        # 加载分词器
        print("加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_dir,
            trust_remote_code=True
        )
        
        # 加载模型
        print("加载模型...")
        model = AutoModelForCausalLM.from_pretrained(
            model_dir,
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None,
            trust_remote_code=True
        )
        
        if device == "cpu":
            model = model.to(device)
        
        print("模型加载完成!")
        return model, tokenizer, device
        
    except Exception as e:
        print(f"模型加载失败: {e}")
        return None, None, None

def test_model(model, tokenizer, device):
    """测试模型推理"""
    print("=== 模型测试 ===")
    
    test_prompts = [
        "你好，请介绍一下你自己。",
        "请解释一下人工智能的基本概念。",
        "写一首关于春天的短诗。"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n测试 {i}: {prompt}")
        print("-" * 50)
        
        try:
            # 编码输入
            inputs = tokenizer(prompt, return_tensors="pt").to(device)
            
            # 生成回复
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=200,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # 解码输出
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            # 移除输入部分，只显示生成的内容
            response = response[len(prompt):].strip()
            
            print(f"回复: {response}")
            
        except Exception as e:
            print(f"推理失败: {e}")

def interactive_chat(model, tokenizer, device):
    """交互式对话"""
    print("\n=== 交互式对话 ===")
    print("输入 'quit' 或 'exit' 退出对话")
    print("=" * 50)
    
    while True:
        try:
            user_input = input("\n用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见!")
                break
            
            if not user_input:
                continue
            
            # 编码输入
            inputs = tokenizer(user_input, return_tensors="pt").to(device)
            
            # 生成回复
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=300,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # 解码输出
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = response[len(user_input):].strip()
            
            print(f"助手: {response}")
            
        except KeyboardInterrupt:
            print("\n\n对话已中断，再见!")
            break
        except Exception as e:
            print(f"生成回复时出错: {e}")

def main():
    """主函数"""
    print("MiDashengLM-7B 模型部署脚本")
    print("=" * 50)
    
    # 检查环境
    check_environment()
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，退出程序")
        return
    
    # 下载模型
    model_dir = download_model()
    if not model_dir:
        print("模型下载失败，退出程序")
        return
    
    # 加载模型
    model, tokenizer, device = load_model(model_dir)
    if model is None:
        print("模型加载失败，退出程序")
        return
    
    # 测试模型
    test_model(model, tokenizer, device)
    
    # 交互式对话
    try:
        interactive_chat(model, tokenizer, device)
    except KeyboardInterrupt:
        print("\n程序已退出")

if __name__ == "__main__":
    main()
