# MiDashengLM-7B 模型部署指南

小米声音理解大模型 MiDashengLM-7B 的下载和部署教程。

## 模型简介

MiDashengLM-7B 是小米开源的声音理解大模型，具有以下特点：

- 7B 参数规模，性能优异
- 支持音频理解和文本生成
- 在 22 个公开评测中达到 SOTA 性能
- 推理速度相比同类模型提升 20 倍

**模型链接**: https://modelscope.cn/models/midasheng/midashenglm-7b

## 环境要求

### 硬件要求

- **推荐**: NVIDIA GPU (8GB+ 显存)
- **最低**: CPU (需要更多内存，推理较慢)
- **内存**: 16GB+ RAM
- **存储**: 20GB+ 可用空间

### 软件要求

- Python 3.8+
- CUDA 11.8+ (如使用 GPU)

## 快速开始

### 方法 1: 一键部署

```bash
python quick_deploy.py
```

### 方法 2: 完整部署

```bash
python deploy_midashenglm.py
```

### 方法 3: 手动安装

1. **安装依赖**

```bash
pip install -r requirements.txt
```

2. **下载模型**

```python
from modelscope import snapshot_download

model_dir = snapshot_download(
    "midasheng/midashenglm-7b",
    cache_dir="/mnt/2T-mac/models"
)
```

3. **加载和使用模型**

```python
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# 加载模型
tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(
    model_dir,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True
)

# 推理示例
prompt = "你好，请介绍一下你自己。"
inputs = tokenizer(prompt, return_tensors="pt").to("cuda")

with torch.no_grad():
    outputs = model.generate(
        **inputs,
        max_new_tokens=200,
        temperature=0.7,
        do_sample=True
    )

response = tokenizer.decode(outputs[0], skip_special_tokens=True)
print(response[len(prompt):].strip())
```

## 文件说明

- `deploy_midashenglm.py`: 完整的部署脚本，包含环境检查、模型下载、测试和交互式对话
- `quick_deploy.py`: 快速部署脚本，适合快速测试
- `requirements.txt`: Python 依赖包列表
- `README.md`: 本说明文档

## 使用示例

### 基本对话

```python
prompt = "请解释一下人工智能的基本概念。"
# ... 推理代码 ...
```

### 创意写作

```python
prompt = "写一首关于春天的短诗。"
# ... 推理代码 ...
```

### 问答系统

```python
prompt = "什么是深度学习？它有哪些应用？"
# ... 推理代码 ...
```

## 性能优化

### GPU 优化

- 使用 `torch.float16` 减少显存占用
- 启用 `device_map="auto"` 自动分配设备
- 调整 `max_new_tokens` 控制生成长度

### CPU 优化

- 使用 `torch.float32` 确保精度
- 减少 `max_new_tokens` 提高速度
- 考虑使用量化版本

## 常见问题

### Q: 显存不足怎么办？

A:

1. 使用 CPU 推理：移除 `device_map="auto"`
2. 使用更小的 batch size
3. 启用梯度检查点：`gradient_checkpointing=True`

### Q: 下载速度慢？

A:

1. 使用国内镜像源
2. 设置代理
3. 分段下载

### Q: 推理速度慢？

A:

1. 使用 GPU 加速
2. 减少生成长度
3. 调整采样参数

## 技术支持

- **模型页面**: https://modelscope.cn/models/midasheng/midashenglm-7b
- **魔搭社区**: https://modelscope.cn/
- **GitHub Issues**: 在对应仓库提交问题

## 许可证

请参考模型页面的许可证信息。

## 更新日志

- 2025-08-06: 初始版本发布
- 支持魔搭社区模型下载
- 提供完整部署脚本
